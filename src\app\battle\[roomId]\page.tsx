import { redirect } from "next/navigation";
import { createClient } from "../../../../supabase/server";
import { SupabaseClient } from "@supabase/supabase-js";
import { useState } from "react";
import GameContainer from "@/components/game-container";
import { User } from "@/interfaces/interfaces"; // Import the User interface

// Function to check if user is in an active match
async function checkUserInActiveMatch(supabase: SupabaseClient, userId: string) {
  try {
    // Query to find active matches that the user is part of
    const { data, error } = await supabase
      .from('matches')
      .select(`
        id,
        difficulty,
        status,
        match_players!inner(player_id)
      `)
      .in('status', ['waiting', 'ongoing'])
      .eq('match_players.player_id', userId);

    if (error) {
      console.error('Error checking if user is in active match:', error);
      return null;
    }

    if (!data || data.length === 0) {
      return null;
    }

    // Return the first active match
    const match = data[0];
    return {
      id: match.id,
      difficulty: match.difficulty,
      status: match.status
    };
  } catch (error) {
    console.error('Exception checking if user is in active match:', error);
    return null;
  }
}

export default async function BattleRoomPage({
  params,
}: {
  params: { roomId: string };
}) {

  const supabase = await createClient();


  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    return redirect("/sign-in");
  }

  // Check if user is already in an active match
  const activeMatch = await checkUserInActiveMatch(supabase, user.id);

  // If user is in an active match with a different difficulty, redirect them to that match
  if (activeMatch) {
    const currentRoomId = params.roomId.toLowerCase();
    const userMatchDifficulty = activeMatch.difficulty.toLowerCase().replace(/\s+/g, "-");

    if (userMatchDifficulty !== currentRoomId) {
      // User is trying to access a different room than they're already in
      console.log(`User is already in a match with difficulty ${activeMatch.difficulty}. Redirecting to that match.`);
      // return redirect(`/battle/${userMatchDifficulty}`);
    }
  }

  // Find the active match for this user and difficulty
  const difficulty = params.roomId.toLowerCase();
  let matchId = null;

  try {
    const { data, error } = await supabase
      .from('matches')
      .select(`
        id,
        match_players!inner(player_id)
      `)
      .in('status', ['waiting', 'ongoing'])
      .eq('difficulty', difficulty)
      .eq('match_players.player_id', user.id);

    if (error) {
      console.error("Error finding match:", error);
    } else if (data && data.length > 0) {
      matchId = data[0].id;
    }
  } catch (error) {
    console.error("Exception finding match:", error);
  }

  // Define room configurations based on roomId
  const roomConfigs = {
    easy: {
      name: "Easy",
      color: "text-green-500",
      bgColor: "bg-green-100",
    },
    medium: {
      name: "Medium",
      color: "text-blue-500",
      bgColor: "bg-blue-100",
    },
    hard: {
      name: "Hard",
      color: "text-orange-500",
      bgColor: "bg-orange-100",
    },
    extreme: {
      name: "Extreme",
      color: "text-purple-500",
      bgColor: "bg-purple-100",
    },
  };

  // Get room config based on roomId or default to easy if not found
  const roomConfig =
    roomConfigs[params.roomId as keyof typeof roomConfigs] || roomConfigs.easy;

  return (
    <>
      <GameContainer
        roomId={params.roomId}
        roomName={roomConfig.name}
        roomColor={roomConfig.color}
        roomBgColor={roomConfig.bgColor}
        initialTimeInSeconds={30}
        matchId={matchId || undefined}
        currentUser={user as User} // Pass the user object as currentUser prop
      />
    </>
  );
}
