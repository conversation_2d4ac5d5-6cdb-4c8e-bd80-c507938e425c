"use client";

import { useEffect, useState, useCallback, useRef } from "react";
import { createClient } from "../../supabase/client";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { BookOpen, Zap, Flame, Crown, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { Difficulty } from "@/utils/audio-utils";

// Define types for room counts
type RoomCounts = {
  easy: number;
  medium: number;
  hard: number;
  "extreme": number;
};

// Define type for match data
type MatchData = {
  id: string;
  difficulty: Difficulty | null;
  match_players: { player_id: string }[];
};

type RoomCardsProps = {
  initialRoomCounts: RoomCounts;
};

export default function RoomCards({ initialRoomCounts }: RoomCardsProps) {
  const [roomCounts, setRoomCounts] = useState<RoomCounts>(initialRoomCounts);
  const [supabase] = useState(() => createClient());
  const [joiningRoom, setJoiningRoom] = useState<string | null>(null);
  const router = useRouter();
  const refreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Room configurations
  const difficultyRooms = [
    {
      name: "Easy",
      icon: BookOpen,
      color: "text-green-500",
      bgColor: "bg-green-100",
    },
    {
      name: "Medium",
      icon: Zap,
      color: "text-blue-500",
      bgColor: "bg-blue-100",
    },
    {
      name: "Hard",
      icon: Flame,
      color: "text-orange-500",
      bgColor: "bg-orange-100",
    },
    {
      name: "Extreme",
      icon: Crown,
      color: "text-purple-500",
      bgColor: "bg-purple-100",
    },
  ];

  // Function to get player counts for each room
  const getRoomPlayerCounts = async (): Promise<RoomCounts> => {
    const { data, error } = await supabase
      .from('matches')
      .select(`
        id,
        difficulty,
        match_players(player_id)
      `)
      .in('status', ['waiting', 'ongoing']);

    if (error) {
      console.error('Error fetching room counts:', error);
      return { ...roomCounts };
    }

    // Process the data to count players per difficulty level
    const newRoomCounts: RoomCounts = {
      easy: 0,
      medium: 0,
      hard: 0,
      "extreme": 0
    };

    (data as MatchData[]).forEach(match => {
      if (!match.difficulty) return;

      const difficulty = match.difficulty.toLowerCase();
      if (difficulty in newRoomCounts) {
        newRoomCounts[difficulty as keyof RoomCounts] += match.match_players.length;
      }
    });

    return newRoomCounts;
  };

  // Function to check if user is registered in any active match and return the match details
  const checkUserInAnyActiveMatch = async (userId: string) => {
    try {
      // First, check if the user is in any match_players records
      const { data: playerMatches, error: playerMatchesError } = await supabase
        .from('match_players')
        .select('match_id')
        .eq('player_id', userId);

      if (playerMatchesError) {
        console.error('Error checking if user is in any match:', playerMatchesError);
        return null;
      }

      if (!playerMatches || playerMatches.length === 0) {
        return null;
      }

      // Get the match IDs where the user is a player
      const matchIds = playerMatches.map(pm => pm.match_id);
      console.log('User is in matches with IDs:', matchIds);

      // Now query for active matches with these IDs
      const { data: activeMatches, error: matchesError } = await supabase
        .from('matches')
        .select('id, difficulty, status')
        .in('id', matchIds)
        .in('status', ['waiting', 'ongoing']);

      if (matchesError) {
        console.error('Error checking active matches:', matchesError);
        return null;
      }

      if (!activeMatches || activeMatches.length === 0) {
        console.log('User has no active matches');
        return null;
      }

      console.log('User active matches:', activeMatches);

      // Return the first active match
      return {
        id: activeMatches[0].id,
        difficulty: activeMatches[0].difficulty,
        status: activeMatches[0].status
      };
    } catch (error) {
      console.error('Exception checking if user is in any active match:', error);
      return null;
    }
  };

  // Function to refresh room counts
  const refreshRoomCounts = async () => {
    try {
      // console.log('Refreshing room counts...');
      const counts = await getRoomPlayerCounts();
      setRoomCounts(counts);
      // console.log('Room counts updated:', counts);
    } catch (error) {
      console.error('Error refreshing room counts:', error);
    }
  };

  // Debounced version of refresh function
  const debouncedRefreshRoomCounts = useCallback(() => {
    if (refreshTimeoutRef.current) {
      clearTimeout(refreshTimeoutRef.current);
    }
    refreshTimeoutRef.current = setTimeout(() => {
      refreshRoomCounts();
    }, 500); // 500ms debounce
  }, []);

  // Function to join a room
  const joinRoom = async (roomName: string) => {
    try {
      setJoiningRoom(roomName);

      // Get current user
      const { data: { user }, error: userError } = await supabase.auth.getUser();

      if (userError || !user) {
        toast.error("You must be logged in to join a room");
        setJoiningRoom(null);
        return;
      }

      // Check if user is already in any active match
      const activeMatch = await checkUserInAnyActiveMatch(user.id);

      if (activeMatch) {
        const currentDifficulty = roomName.toLowerCase();
        const userMatchDifficulty = activeMatch.difficulty.toLowerCase();

        if (userMatchDifficulty !== currentDifficulty) {
          // User is already in a different difficulty room
          toast.error(`You are already in the ${activeMatch.difficulty} room. Please leave that room first before joining the ${roomName} room.`);

          // Navigate to the user's current room
          const formattedDifficulty = userMatchDifficulty.replace(/\s+/g, "-");
          // router.push(`/battle/${formattedDifficulty}`);
          setJoiningRoom(null);
          return;
        }

        // User is already in this room, just navigate there
        const formattedDifficulty = userMatchDifficulty.replace(/\s+/g, "-");
        router.push(`/battle/${formattedDifficulty}`);
        setJoiningRoom(null);
        return;
      }

      // First, check if the user already has a player record
      let playerId: string = user.id; // Use the user's ID as the player ID

      try {
        const { data: existingPlayer, error: fetchError } = await supabase
          .from('players')
          .select('id')
          .eq('id', user.id)
          .single();

        if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "No rows returned" error
          console.error("Error checking for existing player:", fetchError);
          // Continue anyway, we'll try to create the player
        }

        if (!existingPlayer) {
          // Create a player record if it doesn't exist
          const { data: newPlayer, error: playerError } = await supabase
            .from('players')
            .insert({
              id: user.id,
              display_name: user.email || 'Anonymous Player',
              avatar_url: user.user_metadata?.avatar_url || null,
              stats: { games_played: 0, wins: 0 }
            })
            .select('id')
            .single();

          if (playerError) {
            console.error("Error creating player profile:", playerError);
            // If we can't create a player, we'll still try to join with the user ID
            toast.warning("Could not create player profile, but trying to join anyway");
          } else if (newPlayer) {
            playerId = newPlayer.id;
          }
        } else {
          playerId = existingPlayer.id;
        }
      } catch (error) {
        console.error("Error in player check/creation:", error);
        // Continue with the user ID as the player ID
      }

      // Check for matches with the specified difficulty
      const difficulty = roomName.toLowerCase();
      let matchId: string;

      try {
        // First, check specifically for waiting matches with this difficulty
        const { data: waitingMatches, error: waitingMatchesError } = await supabase
          .from('matches')
          .select('id')
          .eq('status', 'waiting')
          .eq('difficulty', difficulty);

        if (waitingMatchesError) {
          console.error("Error fetching waiting matches:", waitingMatchesError);
          toast.error("Failed to check for waiting matches");
          setJoiningRoom(null);
          return;
        }

        // If there are waiting matches, use the first one
        if (waitingMatches && waitingMatches.length > 0) {
          matchId = waitingMatches[0].id;
        } else {
          // No waiting matches found, create a new one

          try {
            // Generate a UUID for the new match
            const matchUuid = crypto.randomUUID();

            // Create a new match
            const { data: newMatch, error: matchError } = await supabase
              .from('matches')
              .insert({
                id: matchUuid,
                difficulty: difficulty,
                status: 'waiting',
                current_round: 0,
                current_word_id: 0,
                current_state: 'waiting',
                created_at: new Date().toISOString(),
                waiting_time: new Date().toISOString(),
                start_time: null, // Will be set when there are at least 2 players
                is_locked: false // Initialize lock status
              })
              .select('id')
              .single();

            if (matchError) {
              console.error("Error creating new match:", matchError);
              toast.error("Failed to create a new match");
              setJoiningRoom(null);
              return;
            }

            if (newMatch) {
              matchId = newMatch.id;
            } else {
              toast.error("Failed to create a new match");
              setJoiningRoom(null);
              return;
            }
          } catch (createError) {
            console.error("Exception creating match:", createError);
            toast.error("Failed to create a new match");
            setJoiningRoom(null);
            return;
          }
        }

        // Check if player is already in this match
        try {
          const { data: existingMatchPlayer, error: playerCheckError } = await supabase
            .from('match_players')
            .select('match_id')
            .eq('match_id', matchId)
            .eq('player_id', playerId)
            .single();

          if (playerCheckError && playerCheckError.code !== 'PGRST116') {
            console.error("Error checking if player is in match:", playerCheckError);
          }

          if (!existingMatchPlayer) {
            // Add player to the match
            const { error: joinError } = await supabase
              .from('match_players')
              .insert({
                match_id: matchId,
                player_id: playerId,
                score: 0,
                lives: 3,
                is_spectator: false,
                created_at: new Date().toISOString()
              });

            if (joinError) {
              console.error("Error joining match:", joinError);
              toast.error("Failed to join the match");
              setJoiningRoom(null);
              return;
            }

            // Immediately refresh room counts after successfully joining
            await refreshRoomCounts();
          }
        } catch (playerError) {
          console.error("Exception checking/adding player to match:", playerError);
          toast.error("Failed to join the match");
          setJoiningRoom(null);
          return;
        }
      } catch (error) {
        console.error("Exception in match handling:", error);
        toast.error("Failed to process match data");
        setJoiningRoom(null);
        return;
      }

      // Navigate to the room page
      toast.success(`Joined ${roomName} room successfully!`);
      router.push(`/battle/${roomName.toLowerCase().replace(/\s+/g, "-")}`);
    } catch (error) {
      console.error("Error joining room:", error);
      toast.error("An unexpected error occurred");
    }
  };

  useEffect(() => {
    // Set up realtime subscription to keep counts updated
    const channel = supabase
      .channel('room-changes')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'match_players'
        },
        async (payload) => {
          // console.log('New player added to match:', payload);

          // Check if the match has status 'waiting' before refreshing counts
          if (payload.new && payload.new.match_id) {
            const { data: match, error } = await supabase
              .from('matches')
              .select('status')
              .eq('id', payload.new.match_id)
              .single();

            if (!error && match && match.status === 'waiting') {
              // console.log('Player added to waiting match, refreshing room counts');
              // Use debounced refresh to prevent too many rapid calls
              debouncedRefreshRoomCounts();
            } else {
              console.log('Player added to non-waiting match, skipping refresh');
            }
          }
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'match_players'
        },
        async (payload) => {
          // console.log('Player removed from match:', payload);

          // Check if the match has status 'waiting' before refreshing counts
          if (payload.old && payload.old.match_id) {
            const { data: match, error } = await supabase
              .from('matches')
              .select('status')
              .eq('id', payload.old.match_id)
              .single();

            if (!error && match && match.status === 'waiting') {
              // console.log('Player removed from waiting match, refreshing room counts');
              // Use debounced refresh to prevent too many rapid calls
              debouncedRefreshRoomCounts();
            } else {
              // console.log('Player removed from non-waiting match, skipping refresh');
            }
          }
        }
      )
      .subscribe();

    // Initial load of room counts
    refreshRoomCounts();

    // Clean up subscription and timeout on unmount
    return () => {
      supabase.removeChannel(channel);
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }
    };
  }, [supabase, debouncedRefreshRoomCounts]);

  return (
    <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {difficultyRooms.map((room) => {
        // Convert room name to match the keys in RoomCounts
        const roomKey = room.name.toLowerCase() as keyof RoomCounts;
        const userCount = roomCounts[roomKey] || 0;

        return (
          <Card
            key={room.name}
            className="border-2 hover:border-amber-500 transition-all duration-200 hover:shadow-lg cursor-pointer bg-white overflow-hidden"
          >
            <CardHeader className={cn("pb-2", room.bgColor)}>
              <div className="flex justify-between items-center">
                <CardTitle className="text-xl font-bold">
                  {room.name}
                </CardTitle>
                <room.icon className={cn("h-6 w-6", room.color)} />
              </div>
              <CardDescription>Spelling challenge</CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="flex items-center justify-center h-24 bg-amber-50/50 rounded-lg border border-amber-100">
                <div className="text-center">
                  <p className="text-4xl font-bold text-amber-800">
                    {userCount}
                  </p>
                  <p className="text-xs text-amber-600">players inside</p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-center pb-4">
              <Button
                onClick={() => joinRoom(room.name)}
                disabled={joiningRoom !== null} // Disable all buttons when joining any room
                className={cn(
                  "px-4 py-2 text-white rounded-full text-sm font-medium transition-colors",
                  joiningRoom === room.name
                    ? "bg-amber-700" // Highlight the room being joined
                    : joiningRoom !== null
                      ? "bg-gray-400" // Gray out other rooms
                      : "bg-amber-600 hover:bg-amber-700" // Normal state
                )}
              >
                {joiningRoom === room.name ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Joining...
                  </>
                ) : joiningRoom !== null ? (
                  <>
                    <span className="text-xs">Join room</span>
                  </>
                ) : (
                  "Join Room"
                )}
              </Button>
            </CardFooter>
          </Card>
        );
      })}
    </section>
  );
}
